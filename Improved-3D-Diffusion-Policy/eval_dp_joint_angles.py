#!/usr/bin/env python3

import sys
sys.stdout = open(sys.stdout.fileno(), mode='w', buffering=1)
sys.stderr = open(sys.stderr.fileno(), mode='w', buffering=1)

import hydra
import time
from omegaconf import OmegaConf
import pathlib
from diffusion_policy_3d.workspace.base_workspace import BaseWorkspace
import tqdm
import torch
import os
import numpy as np
from termcolor import cprint
from torch.utils.data import DataLoader
from diffusion_policy_3d.common.pytorch_util import dict_apply
import copy
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import argparse

os.environ['WANDB_SILENT'] = "True"
OmegaConf.register_new_resolver("eval", eval, replace=True)

# Joint names for the 25-dimensional action space
# Based on joint32_to_joint25 mapping in gr1_action_util.py:
# waist 1 + head 2 + arm 5*2 + hand 6*2 = 25
# Each arm uses 5 out of 7 joints (skipping 2 joints per arm)
JOINT_NAMES = [
    # Waist (1 joint)
    "waist_pitch",              # 0: joint32[1] -> joint25[0]

    # Head (2 joints)
    "head_pitch",               # 1: joint32[3] -> joint25[1]
    "head_yaw",                 # 2: joint32[5] -> joint25[2]

    # Left arm (5 joints)
    "left_shoulder_pitch",      # 3: joint32[6] -> joint25[3]
    "left_shoulder_roll",       # 4: joint32[7] -> joint25[4]
    "left_shoulder_yaw",        # 5: joint32[8] -> joint25[5]
    "left_elbow_pitch",         # 6: joint32[9] -> joint25[6]
    "left_elbow_yaw",           # 7: joint32[11] -> joint25[7]

    # Right arm (5 joints)
    "right_shoulder_pitch",     # 8: joint32[13] -> joint25[8]
    "right_shoulder_roll",      # 9: joint32[14] -> joint25[9]
    "right_shoulder_yaw",       # 10: joint32[15] -> joint25[10]
    "right_elbow_pitch",        # 11: joint32[16] -> joint25[11]
    "right_elbow_yaw",          # 12: joint32[18] -> joint25[12]

    # Left hand (6 joints)
    "left_thumb_pitch",         # 13: joint32[19] -> joint25[13]
    "left_thumb_yaw",           # 14: joint32[20] -> joint25[14]
    "left_index_pitch",         # 15: joint32[21] -> joint25[15]
    "left_middle_pitch",        # 16: joint32[22] -> joint25[16]
    "left_ring_pitch",          # 17: joint32[23] -> joint25[17]
    "left_pinky_pitch",         # 18: joint32[24] -> joint25[18]

    # Right hand (6 joints)
    "right_thumb_pitch",        # 19: joint32[25] -> joint25[19]
    "right_thumb_yaw",          # 20: joint32[26] -> joint25[20]
    "right_index_pitch",        # 21: joint32[27] -> joint25[21]
    "right_middle_pitch",       # 22: joint32[28] -> joint25[22]
    "right_ring_pitch",         # 23: joint32[29] -> joint25[23]
    "right_pinky_pitch",        # 24: joint32[30] -> joint25[24]
]

def evaluate_dp_joint_angles(workspace, cfg, checkpoint_path=None):
    """
    Evaluate Diffusion Policy model on dataset using real joint angle errors
    """
    cprint("=" * 60, "green")
    cprint("DP Joint Angle Evaluation", "green")
    cprint("=" * 60, "green")
    
    # Setup dataset first to get normalizer
    dataset = hydra.utils.instantiate(cfg.task.dataset)

    # Get normalizer from dataset
    normalizer = dataset.get_normalizer()

    # Validate normalizer
    if normalizer is None:
        cprint("Error: Failed to get normalizer from dataset", "red")
        return None

    cprint(f"Normalizer keys: {list(normalizer.params_dict.keys())}", "cyan")
    
    # Load model
    if checkpoint_path is not None:
        cprint(f"Loading checkpoint: {checkpoint_path}", "yellow")
        workspace.load_checkpoint(path=checkpoint_path)
    else:
        cprint("Using latest checkpoint", "yellow")

    policy = workspace.get_model()
    policy.eval()

    device = torch.device(cfg.training.device)
    policy = policy.to(device)

    # Set normalizer to policy and move to device
    policy.set_normalizer(normalizer)
    # Ensure normalizer is on the correct device
    policy.normalizer = policy.normalizer.to(device)

    # Print validation set info
    from diffusion_policy_3d.common.sampler import get_val_mask
    val_mask = get_val_mask(
        n_episodes=dataset.replay_buffer.n_episodes,
        val_ratio=cfg.task.dataset.val_ratio,
        seed=cfg.task.dataset.seed
    )
    
    val_indices = np.where(val_mask)[0]
    cprint(f"Total episodes: {dataset.replay_buffer.n_episodes}", "cyan")
    cprint(f"Validation episodes: {len(val_indices)}", "cyan")
    cprint(f"Validation ratio: {cfg.task.dataset.val_ratio}", "cyan")

    # Create validation dataset
    val_dataset = copy.deepcopy(dataset)
    val_dataset.replay_buffer = val_dataset.replay_buffer.get_episode_slice(val_indices)
    
    # Create dataloader
    val_dataloader = DataLoader(
        val_dataset,
        batch_size=cfg.training.batch_size,
        shuffle=False,
        num_workers=0,  # Set to 0 to avoid multiprocessing issues
        pin_memory=True,
        persistent_workers=False
    )

    cprint(f"Validation dataloader length: {len(val_dataloader)}", "cyan")

    # Get model parameters for evaluation
    n_obs_steps = policy.n_obs_steps
    horizon = policy.horizon
    
    cprint(f"Model n_obs_steps: {n_obs_steps}", "cyan")
    cprint(f"Model horizon: {horizon}", "cyan")

    # Collect predictions and ground truth
    all_pred_actions = []
    all_gt_actions = []

    with torch.no_grad():
        for batch_idx, batch in enumerate(tqdm.tqdm(val_dataloader, desc="Evaluating DP")):
            # Move data to device - manually ensure all tensors are on the correct device
            def move_to_device(data):
                if isinstance(data, dict):
                    return {k: move_to_device(v) for k, v in data.items()}
                elif isinstance(data, (list, tuple)):
                    return type(data)(move_to_device(item) for item in data)
                elif isinstance(data, torch.Tensor):
                    return data.to(device)
                else:
                    return data

            batch = move_to_device(batch)
            obs_dict = batch['obs']
            gt_action = batch['action']  # [B, horizon, 25]

            try:
                # Model prediction
                result = policy.predict_action(obs_dict)
                pred_action_full = result['action_pred']  # [B, horizon, 25] - full prediction
                # pred_action_exec = result['action']       # [B, n_action_steps, 25] - executable actions (not used)

                # For evaluation, we want to compare the full prediction with GT
                # But we need to align them properly based on the time offset

                # The model predicts actions starting from step (n_obs_steps - 1)
                # So pred_action_full[:, 0, :] corresponds to gt_action[:, n_obs_steps-1, :]

                # Unnormalize both predictions and ground truth to get real joint angles
                pred_action_real = normalizer['action'].unnormalize(pred_action_full)
                gt_action_real = normalizer['action'].unnormalize(gt_action)

                # Determine how many steps we can compare
                pred_steps = pred_action_real.shape[1]  # horizon
                gt_steps = gt_action_real.shape[1]      # horizon
                max_compare_steps = min(pred_steps, gt_steps - (n_obs_steps - 1))

                if max_compare_steps > 0:
                    # Extract aligned portions
                    pred_aligned = pred_action_real[:, :max_compare_steps, :]  # [B, max_compare_steps, 25]
                    gt_aligned = gt_action_real[:, (n_obs_steps-1):(n_obs_steps-1+max_compare_steps), :]  # [B, max_compare_steps, 25]

                    # Convert to numpy and store
                    all_pred_actions.append(pred_aligned.cpu().numpy())
                    all_gt_actions.append(gt_aligned.cpu().numpy())
                else:
                    cprint(f"Warning: Batch {batch_idx} has insufficient steps for comparison", "yellow")

            except Exception as e:
                cprint(f"Batch {batch_idx} failed: {e}", "red")
                continue

            # Limit evaluation batches if specified
            if cfg.training.max_val_steps is not None and batch_idx >= cfg.training.max_val_steps - 1:
                break
    
    if not all_pred_actions:
        cprint("Evaluation failed: No successful batches", "red")
        return None
    
    # Concatenate all data
    all_pred_actions = np.concatenate(all_pred_actions, axis=0)  # [N, T, 25]
    all_gt_actions = np.concatenate(all_gt_actions, axis=0)      # [N, T, 25]
    
    N, T, action_dim = all_pred_actions.shape
    cprint(f"Total samples: {N}, Time steps: {T}, Action dimensions: {action_dim}", "cyan")
    
    # Calculate joint angle errors
    joint_errors = analyze_joint_angle_errors(all_pred_actions, all_gt_actions)

    # Create visualizations
    output_dir = pathlib.Path(workspace.output_dir)
    cprint("Creating DP visualizations...", "yellow")

    # Plot full episode predictions for ALL joints (0-24)
    all_joint_indices = list(range(25))  # All 25 joints
    plot_full_episode_predictions(val_dataset, policy, device, JOINT_NAMES, output_dir,
                                episode_idx=0, joint_indices=all_joint_indices)

    # Save results
    results_file = output_dir / 'dp_joint_angle_evaluation.npz'

    results = {
        'pred_actions': all_pred_actions,
        'gt_actions': all_gt_actions,
        'joint_names': JOINT_NAMES,
        **joint_errors
    }

    np.savez(results_file, **results)
    cprint(f"DP Results saved to: {results_file}", "green")

    return results

def analyze_joint_angle_errors(pred_actions, gt_actions):
    """
    Analyze joint angle errors in detail for DP
    """
    _, T, action_dim = pred_actions.shape
    
    # Calculate absolute errors for each joint at each timestep
    abs_errors = np.abs(pred_actions - gt_actions)  # [N, T, 25]
    
    # Overall statistics
    overall_mae = np.mean(abs_errors)
    overall_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2))
    
    # Per-joint statistics (averaged over all samples and timesteps)
    joint_mae = np.mean(abs_errors, axis=(0, 1))  # [25,]
    joint_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2, axis=(0, 1)))  # [25,]
    joint_max_error = np.max(abs_errors, axis=(0, 1))  # [25,]
    
    # Per-timestep statistics (averaged over all samples and joints)
    timestep_mae = np.mean(abs_errors, axis=(0, 2))  # [T,]
    timestep_rmse = np.sqrt(np.mean((pred_actions - gt_actions) ** 2, axis=(0, 2)))  # [T,]
    
    # Print detailed analysis
    cprint("=" * 60, "cyan")
    cprint("DP Joint Angle Error Analysis", "cyan")
    cprint("=" * 60, "cyan")
    
    cprint(f"Overall MAE: {overall_mae:.4f} radians ({np.degrees(overall_mae):.2f} degrees)", "green")
    cprint(f"Overall RMSE: {overall_rmse:.4f} radians ({np.degrees(overall_rmse):.2f} degrees)", "green")
    
    cprint(f"\nPer-Joint Analysis:", "yellow")
    cprint("-" * 80, "white")
    cprint(f"{'Joint':<25} {'MAE (rad)':<12} {'MAE (deg)':<12} {'RMSE (rad)':<12} {'RMSE (deg)':<12} {'Max Error (deg)':<15}", "white")
    cprint("-" * 80, "white")
    
    for i in range(action_dim):
        joint_name = JOINT_NAMES[i] if i < len(JOINT_NAMES) else f"joint_{i}"
        cprint(f"{joint_name:<25} {joint_mae[i]:<12.4f} {np.degrees(joint_mae[i]):<12.2f} "
               f"{joint_rmse[i]:<12.4f} {np.degrees(joint_rmse[i]):<12.2f} {np.degrees(joint_max_error[i]):<15.2f}", "white")
    
    # Find best and worst joints
    best_joint_idx = np.argmin(joint_mae)
    worst_joint_idx = np.argmax(joint_mae)
    
    cprint(f"\nBest Joint: {JOINT_NAMES[best_joint_idx]} (MAE: {np.degrees(joint_mae[best_joint_idx]):.2f} deg)", "green")
    cprint(f"Worst Joint: {JOINT_NAMES[worst_joint_idx]} (MAE: {np.degrees(joint_mae[worst_joint_idx]):.2f} deg)", "red")
    
    cprint(f"\nPer-Timestep Analysis:", "yellow")
    for t in range(T):
        cprint(f"  Timestep {t:2d}: MAE = {timestep_mae[t]:.4f} rad ({np.degrees(timestep_mae[t]):.2f} deg)", "white")
    
    best_timestep = np.argmin(timestep_mae)
    worst_timestep = np.argmax(timestep_mae)
    cprint(f"\nBest Timestep: {best_timestep} (MAE: {np.degrees(timestep_mae[best_timestep]):.2f} deg)", "green")
    cprint(f"Worst Timestep: {worst_timestep} (MAE: {np.degrees(timestep_mae[worst_timestep]):.2f} deg)", "red")
    
    return {
        'overall_mae': overall_mae,
        'overall_rmse': overall_rmse,
        'joint_mae': joint_mae,
        'joint_rmse': joint_rmse,
        'joint_max_error': joint_max_error,
        'timestep_mae': timestep_mae,
        'timestep_rmse': timestep_rmse,
        'abs_errors': abs_errors
    }


def plot_full_episode_predictions(dataset, policy, device, joint_names, output_dir,
                                episode_idx=0, joint_indices=None):
    """
    Plot full episode predictions showing GT trajectory and all prediction segments for DP

    Args:
        dataset: validation dataset
        policy: trained DP policy model
        device: device to run inference on
        joint_names: list of joint names
        output_dir: directory to save plots
        episode_idx: which episode to visualize
        joint_indices: list of joint indices to plot
    """
    if joint_indices is None:
        joint_indices = list(range(25))  # Default: all 25 joints

    cprint(f"Plotting full episode predictions for DP (episode {episode_idx})...", "yellow")

    # Get episode data
    episode_data = dataset.replay_buffer.get_episode(episode_idx)
    obs_data = episode_data['obs']
    action_data = episode_data['action']

    episode_length = len(action_data)
    n_obs_steps = policy.n_obs_steps
    horizon = policy.horizon

    cprint(f"Episode length: {episode_length}, n_obs_steps: {n_obs_steps}, horizon: {horizon}", "cyan")

    # Get ground truth trajectory (unnormalized)
    gt_trajectory = dataset.get_normalizer()['action'].unnormalize(
        torch.from_numpy(action_data).float()
    ).numpy()

    # Apply point cloud processing
    import diffusion_policy_3d.model.vision_3d.point_process as point_process

    # Create new array with correct shape for processed point clouds
    processed_point_clouds = np.zeros((episode_length, dataset.num_points, obs_data['point_cloud'].shape[-1]))

    for i in range(episode_length):
        # point_process.uniform_sampling_numpy expects shape [B, N, C], but we have [N, C]
        # So we need to add batch dimension
        pc = obs_data['point_cloud'][i]  # [N, C]
        pc_batch = pc[np.newaxis, ...]  # [1, N, C]
        pc_sampled = point_process.uniform_sampling_numpy(pc_batch, dataset.num_points)  # [1, num_points, C]
        processed_point_clouds[i] = pc_sampled[0]  # [num_points, C]

    # Generate predictions for each step
    pred_trajectories = []

    with torch.no_grad():
        for step_idx in range(n_obs_steps - 1, episode_length):
            # Get observation window
            start_idx = step_idx - (n_obs_steps - 1)
            end_idx = step_idx + 1

            # Create observation dictionary
            obs_dict = {}
            for key in obs_data.keys():
                if key == 'point_cloud':
                    obs_dict[key] = processed_point_clouds[start_idx:end_idx]  # [n_obs_steps, num_points, C]
                else:
                    obs_dict[key] = obs_data[key][start_idx:end_idx]  # [n_obs_steps, ...]

            # Convert to torch tensors and add batch dimension
            for key, value in obs_dict.items():
                obs_dict[key] = torch.from_numpy(value).float().unsqueeze(0).to(device)  # [1, n_obs_steps, ...]

            try:
                # Get prediction
                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']
                pred_traj = pred_action[0].cpu().numpy()  # [horizon, 25]
                pred_trajectories.append(pred_traj)

            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    cprint(f"GPU OOM at step {step_idx}, clearing cache and retrying", "yellow")
                    torch.cuda.empty_cache()
                    try:
                        result = policy.predict_action(obs_dict)
                        pred_action = result['action_pred']
                        pred_traj = pred_action[0].cpu().numpy()
                        pred_trajectories.append(pred_traj)
                    except Exception as retry_e:
                        cprint(f"Retry failed at step {step_idx}: {retry_e}", "red")
                        pred_traj = np.zeros((horizon, 25))
                        pred_trajectories.append(pred_traj)
                else:
                    cprint(f"Runtime error at step {step_idx}: {e}", "red")
                    pred_traj = np.zeros((horizon, 25))
                    pred_trajectories.append(pred_traj)
            except Exception as e:
                cprint(f"Unexpected error at step {step_idx}: {e}", "red")
                pred_traj = np.zeros((horizon, 25))
                pred_trajectories.append(pred_traj)

    # Create plots for each joint
    for joint_idx in joint_indices:
        if joint_idx >= len(joint_names):
            continue

        joint_name = joint_names[joint_idx]

        fig, ax = plt.subplots(1, 1, figsize=(20, 8))

        # Plot GT trajectory
        time_steps = np.arange(episode_length)
        gt_joint_values = gt_trajectory[:, joint_idx]
        ax.plot(time_steps, np.degrees(gt_joint_values), 'k-', linewidth=3,
               label='Ground Truth', alpha=0.9, zorder=10)

        # Plot all prediction segments
        colors = plt.cm.viridis(np.linspace(0, 1, len(pred_trajectories)))

        for step_idx, pred_traj in enumerate(pred_trajectories):
            if step_idx >= episode_length:
                break

            pred_joint_values = pred_traj[:, joint_idx]  # [horizon]

            # CORRECTED Time alignment:
            # The model predicts actions starting from step (step_idx - n_obs_steps + 1)
            # because it uses observations [step_idx - n_obs_steps + 1 : step_idx + 1]
            # and predicts actions starting from the last observation step minus (n_obs_steps - 1)
            pred_start_step = step_idx - (n_obs_steps - 1)
            pred_time_steps = np.arange(pred_start_step,
                                      min(pred_start_step + len(pred_joint_values), episode_length))

            # Only plot if prediction starts within episode bounds
            if pred_start_step >= 0:
                # Truncate prediction if it goes beyond episode length
                if len(pred_time_steps) < len(pred_joint_values):
                    pred_joint_values = pred_joint_values[:len(pred_time_steps)]

                # Plot prediction segment
                ax.plot(pred_time_steps, np.degrees(pred_joint_values),
                       color=colors[step_idx], alpha=0.6, linewidth=1,
                       label=f'DP Pred from step {step_idx}' if step_idx < 5 else "")

        # Formatting
        ax.set_xlabel('Time Step')
        ax.set_ylabel('Joint Angle (degrees)')
        ax.set_title(f'DP {joint_name} - Full Episode Predictions (Episode {episode_idx})')
        ax.grid(True, alpha=0.3)
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')

        # Add statistics
        if len(pred_trajectories) > 0:
            # Calculate error for first prediction step only
            first_pred_errors = []
            for step_idx, pred_traj in enumerate(pred_trajectories):
                if step_idx < len(gt_joint_values):
                    error = abs(pred_traj[0, joint_idx] - gt_joint_values[step_idx])
                    first_pred_errors.append(error)

            if first_pred_errors:
                mean_error = np.mean(first_pred_errors)
                ax.text(0.02, 0.98, f'DP Mean 1-step error: {np.degrees(mean_error):.2f}°',
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        plt.tight_layout()

        # Save plot
        output_path = output_dir / f'dp_full_episode_predictions_{joint_name}_ep{episode_idx}.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        cprint(f"DP Full episode plot saved to: {output_path}", "green")
        plt.close()


@hydra.main(
    version_base=None,
    config_path=str(pathlib.Path(__file__).parent.joinpath(
        'diffusion_policy_3d','config'))
)
def main(cfg: OmegaConf):
    torch.manual_seed(42)
    OmegaConf.resolve(cfg)

    cprint(f"Config: {cfg._target_}", "yellow")
    cprint(f"Task: {cfg.task_name}", "yellow")
    cprint(f"Data path: {cfg.task.dataset.zarr_path}", "yellow")

    cls = hydra.utils.get_class(cfg._target_)
    workspace: BaseWorkspace = cls(cfg)

    # Check if model exists - first try environment variable or command line
    checkpoint_path = None

    # Try environment variable first
    env_checkpoint = os.environ.get('CHECKPOINT_PATH')
    if env_checkpoint:
        checkpoint_path = pathlib.Path(env_checkpoint)
        if not checkpoint_path.exists():
            cprint(f"Warning: Environment checkpoint not found: {env_checkpoint}", "yellow")
            checkpoint_path = None

    # If no checkpoint specified or not found, use default
    if checkpoint_path is None:
        checkpoint_path = workspace.get_checkpoint_path(tag='latest')
        if not checkpoint_path.exists():
            cprint(f"Error: Model file not found {checkpoint_path}", "red")
            return

    cprint(f"Using DP model: {checkpoint_path}", "green")

    # Start evaluation
    results = evaluate_dp_joint_angles(workspace, cfg, checkpoint_path)

    if results:
        cprint("DP Joint angle evaluation completed!", "green")
    else:
        cprint("DP Joint angle evaluation failed!", "red")

if __name__ == "__main__":
    main()
