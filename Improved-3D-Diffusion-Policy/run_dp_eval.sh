#!/bin/bash

# Script to run DP joint angle evaluation
# Usage: ./run_dp_eval.sh [checkpoint_path]

# Activate conda environment
conda activate idp3

# Set checkpoint path if provided as argument
if [ $# -eq 1 ]; then
    export CHECKPOINT_PATH="$1"
    echo "Using checkpoint: $CHECKPOINT_PATH"
else
    echo "Using latest checkpoint from model directory"
fi

# Run DP joint angle evaluation
python eval_dp_joint_angles.py \
    --config-path=/home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy/diffusion_policy_3d/config \
    --config-name=dp_224x224_r3m.yaml \
    task=gr1_dex-image \
    hydra.run.dir=data/outputs/gr1_dex-r3m-finetune_seed0 \
    training.debug=False \
    training.seed=0 \
    training.device="cuda:0" \
    exp_name=gr1_dex-r3m-finetune \
    logging.mode=offline \
    checkpoint.save_ckpt=True \
    task.dataset.zarr_path=/home/<USER>/code/company/torqueidp3/data/raw_pour_converted \
    task.dataset.max_train_episodes=8 \
    task.dataset.val_ratio=0.11 \
    training.checkpoint_every=300

echo "DP Joint angle evaluation completed!"
